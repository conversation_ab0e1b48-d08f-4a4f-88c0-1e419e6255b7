---
apiVersion: v1
kind: Namespace
metadata:
  name: ${K8S_NAMESPACE} 
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: ${CI_PROJECT_NAME}
  namespace: ${K8S_NAMESPACE}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ${CI_PROJECT_NAME}
  template:
    metadata:
      labels:
        app: ${CI_PROJECT_NAME}
    spec:
      imagePullSecrets:
        - name: nexuscred
      containers:
        - name: ${CI_PROJECT_NAME}-container
          image: ${IMAGE_FULL_NAME}:${BUILD_ID}
          env:
            - name: BUILD_ID
              value: ${IMAGE_FULL_NAME}:${BUILD_ID}          
            - name: DATABASE_URI
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: FILLPASSPORT_SERVICE_PROD_DB_CONFIG
            - name: NODE_ENV
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: FILLPASSPORT_SERVICE_NODE_ENV
            - name: SECRET_KEY
              valueFrom:
                secretKeyRef:
                  name: secrets
                  key: FILLPASSPORT_SERVICE_SECRET_KEY 
            
          ports:
            - containerPort: 8080
---
apiVersion: v1
kind: Service
metadata:
  name: ${CI_PROJECT_NAME}-service
  namespace: ${K8S_NAMESPACE}
spec:
  selector:
    app: ${CI_PROJECT_NAME}
  ports:
    - protocol: TCP
      port: 443
      targetPort: 8080
---
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: ${CI_PROJECT_NAME}-ingress
  namespace: ${K8S_NAMESPACE}
  annotations:
    nginx.ingress.kubernetes.io/rewrite-target: /$2
spec:
  ingressClassName: nginx
  tls:
    - hosts:
      - services.post.kz
      secretName: services.post.kz-tls
  rules:
    - host: services.post.kz
      http:
        paths:
          - path: /npi/filpassport(/|$)(.*)
            pathType: Prefix
            backend:
              service:
                name: ${CI_PROJECT_NAME}-service
                port:
                  number: 443
