<!-- HTML for static distribution bundle build -->
<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <title>{{app_name}}</title>
  <link rel="stylesheet" type="text/css" href="{{base_url}}/index.css">
  <link rel="stylesheet" type="text/css" href="{{base_url}}/swagger-ui.css">
  <link rel="icon" type="image/png" href="{{base_url}}/favicon-32x32.png" sizes="32x32" />
  <link rel="icon" type="image/png" href="{{base_url}}/favicon-16x16.png" sizes="16x16" />
</head>

<body>
  <div id="swagger-ui"></div>

  <script src="{{base_url}}/swagger-ui-bundle.js"> </script>
  <script src="{{base_url}}/swagger-ui-standalone-preset.js"> </script>
  <script>
    var server = "{{server}}";
    var config = {
      presets: [
        SwaggerUIBundle.presets.apis,
        SwaggerUIStandalonePreset
      ],
      plugins: [
        SwaggerUIBundle.plugins.DownloadUrl,
          // if server is not none
          {% if server %}
            // add a plugin to alter spec before it rendered
            {
                statePlugins: {
                    spec: {
                        wrapActions: {
                            updateJsonSpec: function(oriAction, system) {
                                return (spec) => {
                                    // change spec.servers here to add new entry, use concat to put it as the first & default one
                                    spec.servers = [{url: server}].concat(spec.servers || [])
                                    return oriAction(spec)
                                }
                            }
                        }
                    }
                }
            }
          {% endif %}
      ]
    };



    var user_config = {{ config_json| safe}};  // User config options provided from Python code
    for (var attrname in user_config) { config[attrname] = user_config[attrname]; }

    window.onload = function () {
      // Build a system
      const ui = SwaggerUIBundle(config)

      {% if oauth_config_json %}
      var oauth_user_config = {{ oauth_config_json| safe
    }};  // OAuth2 user config options provided from Python code
    ui.initOAuth(oauth_user_config);
    {% endif %}

    window.ui = ui
}
  </script>
</body>

</html>