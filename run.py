import os
import json

from app.main.controller import controller
from app.main import create_app, db
from app.main.schema.api_spec import get_apispec
from app.main.swagger import swagger_ui_blueprint

app = create_app(os.getenv('NODE_ENV') or 'dev')

app.register_blueprint(swagger_ui_blueprint)
app.register_blueprint(controller.filp_route)

app.app_context().push()


def run():
    app.run(host='0.0.0.0')


@app.route('/npi')

@app.route('/about')

def hello():
    return 'Документация d <a href="https://kazpostdigital.atlassian.net/wiki/spaces/FilpassportNPI/pages/78676009">https://kazpostdigital.atlassian.net/wiki/spaces/FilpassportNPI/pages/78676009</a>'

@app.route('/swagger')
def create_swagger_spec():
    return json.dumps(get_apispec(app).to_dict())


